/* 自定义轮播图指示器样式 */
.swiper-pagination {
    position: absolute;
    bottom: 20px !important;
    width: 100%;
    text-align: center;
    z-index: 10;
}

.swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    display: inline-block;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    opacity: 1;
    margin: 0 5px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.swiper-pagination-bullet-active {
    background: #015fc9;
    width: 30px;
    border-radius: 10px;
    border: 2px solid #fff;
}

/* 添加分页器容器 */
#main-slider-pagination {
    position: absolute;
    bottom: 20px;
    width: 100%;
    text-align: center;
    z-index: 10;
}

/* 自定义导航按钮样式 */
.main-slider__nav .swiper-button-prev,
.main-slider__nav .swiper-button-next {
    width: 50px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.main-slider__nav .swiper-button-prev:hover,
.main-slider__nav .swiper-button-next:hover {
    border-color: #fff;
    color: #fff;
    background-color: rgba(1, 95, 201, 0.5);
}

/* 箭头图标样式 */
.main-slider__nav .swiper-button-prev i,
.main-slider__nav .swiper-button-next i {
    font-size: 18px;
}
