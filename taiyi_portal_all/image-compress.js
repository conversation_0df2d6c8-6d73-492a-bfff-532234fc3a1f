/**
 * 图片压缩与转换工具
 * 使用方法: node image-compress.js
 * 
 * 注意: 需要先安装相关依赖:
 * npm install sharp glob fs-extra
 */

const sharp = require('sharp');
const glob = require('glob');
const fs = require('fs-extra');
const path = require('path');

// 配置项
const config = {
  // 源图片目录
  sourceDir: 'static/picture',
  // WebP输出目录
  outputDir: 'static/picture/webp',
  // 压缩JPEG/PNG输出目录
  compressedDir: 'static/picture/compressed',
  // WebP质量 (1-100)
  webpQuality: 80,
  // JPEG质量 (1-100)
  jpegQuality: 85,
  // PNG压缩等级 (0-9)
  pngCompressionLevel: 9,
  // 是否生成多种尺寸
  generateSizes: true,
  // 图片尺寸配置 [宽度, 后缀名]
  sizes: [
    [640, 'sm'],  // 小尺寸
    [1024, 'md'], // 中尺寸
    [1920, 'lg']  // 大尺寸
  ]
};

// 确保输出目录存在
fs.ensureDirSync(config.outputDir);
fs.ensureDirSync(config.compressedDir);

// 处理所有图片
async function processImages() {
  // 获取所有图片文件
  const imageFiles = glob.sync(`${config.sourceDir}/**/*.{jpg,jpeg,png,gif}`);
  
  console.log(`找到 ${imageFiles.length} 个图片文件待处理...`);
  
  // 处理每个图片
  for (const imagePath of imageFiles) {
    try {
      const filename = path.basename(imagePath);
      const ext = path.extname(filename).toLowerCase();
      const name = path.basename(filename, ext);
      
      // 加载图片
      const image = sharp(imagePath);
      const metadata = await image.metadata();
      
      // 1. 转换为WebP格式
      const webpPath = path.join(config.outputDir, `${name}.webp`);
      await image.webp({ quality: config.webpQuality }).toFile(webpPath);
      console.log(`WebP转换成功: ${webpPath}`);
      
      // 2. 压缩原格式图片
      const compressedPath = path.join(config.compressedDir, filename);
      
      if (ext === '.jpg' || ext === '.jpeg') {
        await image.jpeg({ quality: config.jpegQuality }).toFile(compressedPath);
      } else if (ext === '.png') {
        await image.png({ compressionLevel: config.pngCompressionLevel }).toFile(compressedPath);
      } else if (ext === '.gif') {
        // GIF不做处理,直接复制
        await fs.copy(imagePath, compressedPath);
      }
      console.log(`压缩成功: ${compressedPath}`);
      
      // 3. 生成多种尺寸(如果启用)
      if (config.generateSizes) {
        for (const [width, suffix] of config.sizes) {
          // 只处理大于此尺寸的图片
          if (metadata.width > width) {
            // WebP版本
            const resizedWebpPath = path.join(config.outputDir, `${name}-${suffix}.webp`);
            await image.resize(width).webp({ quality: config.webpQuality }).toFile(resizedWebpPath);
            
            // 原格式版本
            const resizedPath = path.join(config.compressedDir, `${name}-${suffix}${ext}`);
            if (ext === '.jpg' || ext === '.jpeg') {
              await image.resize(width).jpeg({ quality: config.jpegQuality }).toFile(resizedPath);
            } else if (ext === '.png') {
              await image.resize(width).png({ compressionLevel: config.pngCompressionLevel }).toFile(resizedPath);
            }
            console.log(`已生成 ${width}px 宽度版本`);
          }
        }
      }
    } catch (error) {
      console.error(`处理图片 ${imagePath} 时出错:`, error);
    }
  }
  
  console.log('所有图片处理完成!');
}

// 开始处理
processImages().catch(console.error); 