<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{companyInfo.name}}</title>
    <!-- favicons Icons -->
    
    
    
    
    <meta name="description" content="Insur HTML 5 Template ">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">

    <link href="static/css/css2.css" rel="stylesheet">


    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/animate.min.css">
    <link rel="stylesheet" href="static/css/custom-animate.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/jarallax.css">
    <link rel="stylesheet" href="static/css/jquery.magnific-popup.css">
    <link rel="stylesheet" href="static/css/nouislider.min.css">
    <link rel="stylesheet" href="static/css/nouislider.pips.css">
    <link rel="stylesheet" href="static/css/odometer.min.css">
    <link rel="stylesheet" href="static/css/swiper.min.css">
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/style1.css">
    <link rel="stylesheet" href="static/css/tiny-slider.min.css">
    <link rel="stylesheet" href="static/css/stylesheet.css">
    <link rel="stylesheet" href="static/css/owl.carousel.min.css">
    <link rel="stylesheet" href="static/css/owl.theme.default.min.css">
    <link rel="stylesheet" href="static/css/jquery.bxslider.css">
    <link rel="stylesheet" href="static/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="static/css/vegas.min.css">
    <link rel="stylesheet" href="static/css/jquery-ui.css">
    <link rel="stylesheet" href="static/css/timePicker.css">

    <!-- template styles -->
    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->

    <div id="app">
    <div class="page-wrapper">
        <header class="main-header clearfix">
            <div class="main-header__top">
                <div class="container">
                    <div class="main-header__top-inner">
                        <div class="main-header__top-address">
                            <ul class="list-unstyled main-header__top-address-list">
                                <li>
                                    <i class="icon">
                                        <span class="icon-pin"></span>
                                    </i>
                                    <div class="text">
                                        <p>{{companyInfo.address}}</p>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon">
                                        <span class="icon-email"></span>
                                    </i>
                                    <div class="text">
                                        <p><a href="mailto:<EMAIL>">{{companyInfo.mailbox}}</a></p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <nav class="main-menu clearfix">
                <div class="main-menu__wrapper clearfix">
                    <div class="container">
                        <div class="main-menu__wrapper-inner clearfix">
                            <div class="main-menu__left">
                                <div class="main-menu__logo">
                                    <a><img :src="imgUrl + companyInfo.logoImg" alt="" style="height: 27px"></a>
                                </div>
                                <div class="main-menu__main-menu-box">
                                    <div class="main-menu__main-menu-box-inner">
                                        <a href="#" class="mobile-nav__toggler" @click="expanded = true"><i class="fa fa-bars"></i></a>
                                        <ul class="main-menu__list one-page-scroll-menu">
                                            <li class=" megamenu scrollToLink">
                                                <a href="index-one-page.html">首页 </a>
                                            </li>

                                            <li class="scrollToLink">
                                                <a href="company.html">保险公司 </a>
                                            </li>
                                            <li class="scrollToLink">
                                                <a href="products.html">保险产品</a>
                                            </li>
                                            <li class="scrollToLink current">
                                                <a href="news.html">新闻资讯</a>
                                            </li>
                                            <li class="scrollToLink" style="margin-right: 37px;">
                                                <a href="about.html">关于我们</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </header>

        <div class="stricky-header stricked-menu main-menu">
            <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
        </div><!-- /.stricky-header -->

        <!--Page Header Start-->
        <section class="page-header">
            <div class="page-header-bg" style="background-image: url(assets/images/backgrounds/page-header-bg.jpg)">
            </div>
            <div class="page-header-shape-1"><img src="static/picture/page-header-shape-1.png" alt=""></div>
            <div class="container">
                <div class="page-header__inner">
                    <!-- <ul class="thm-breadcrumb list-unstyled">
                        <li><a>Home</a></li>
                        <li><span>/</span></li>
                        <li>news</li>
                    </ul> -->
                    <h2>新闻资讯</h2>
                </div>
            </div>
        </section>
        <!--Page Header End-->

        <!--News One Start-->
        <section class="news-one" id="news">
            <div class="container">
                <div class="col-xl-4 col-lg-5" style="margin-bottom: 40px;">
                    <div class="sidebar__single sidebar__search">
                        <div class="sidebar__search-form">
                            <input v-model="keyword" type="search" placeholder="搜索新闻名称">
                            <button @click="getNews" type="submit"><i class="icon-magnifying-glass"></i></button>
                        </div>
                    </div>
                </div>
                <div class="section-title text-center">
                    <div class="section-sub-title-box">
                        <p class="section-sub-title">新闻资讯</p>
                        <div class="section-title-shape-1">
                            <img src="static/picture/section-title-shape-1.png" alt="">
                        </div>
                        <div class="section-title-shape-2">
                            <img src="static/picture/section-title-shape-2.png" alt="">
                        </div>
                    </div>
                    <h2 class="section-title__title">{{companyInfo.companyName}}动态</h2>
                </div>
                <div class="row">
                    <!--News One Single Start-->
                    <div class="col-xl-4 col-lg-4 wow fadeInUp" data-wow-delay="100ms"
                        v-for="(item,index) in newsList" :key="index" @click="toDetail(item)">
                        <div class="news-one__single">
                            <div class="news-one__img">
                                <img :src="imgUrl + item.newsImg" alt="">
                                <!-- <div class="news-one__tag">
                                <p><i class="far fa-folder"></i>BUSINESS</p>
                            </div> -->
                                <div class="news-one__arrow-box">
                                    <a  class="news-one__arrow">
                                        <span class="icon-right-arrow1"></span>
                                    </a>
                                </div>
                            </div>
                            <div class="news-one__content">
                                <ul class="list-unstyled news-one__meta">
                                    <li><a ><i
                                                class="far fa-calendar"></i>{{item.messageTiming}}</a>
                                    </li>
                                    <li><a ><i
                                                class="far fa-eye" style="color: #015fc9"></i>{{item.clicksNum}}</a>
                                    </li>
                                </ul>
                                <h3 class="news-one__title"><a >{{item.name}}</a></h3>
                                <div class="news-one__read-more">
                                    <a >详情 <i class="fas fa-angle-double-right"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>
        <!--News One End-->

        <!--Site Footer Start-->
        <footer class="site-footer">
            <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
            </div>
            <div class="container">
                <div class="site-footer__top">
                    <div class="row">
<!--                        <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="100ms">-->
<!--                            <div class="footer-widget__column footer-widget__about">-->
<!--                                <div class="footer-widget__logo">-->
<!--                                    <a><img :src="imgUrl + companyInfo.logoImg" alt="" style="height: 34px"></a>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="200ms">
                            <div class="footer-widget__column footer-widget__contact clearfix">
                                <h3 class="footer-widget__title">公司地址</h3>
                                <ul class="footer-widget__contact-list list-unstyled clearfix">
                                    <li>
                                        <div class="icon">
                                            <span class="icon-pin"></span>
                                        </div>
                                        <div class="text">
                                            <p>{{companyInfo.address}}</p>
                                        </div>
                                    </li>
                                </ul>
                                <div class="footer-widget__open-hour">
                                    <h3 class="footer-widget__open-hour-title">工作时间</h3>
                                    <h3 class="footer-widget__open-hour-text">工作日 早上9:00-18:00</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="400ms">
                            <div class="footer-widget__column footer-widget__newsletter">
                                <h3 class="footer-widget__title">联系电话</h3>
                                <div class="footer-widget__phone">
                                    <div class="footer-widget__phone-icon">
                                        <span class="icon-telephone"></span>
                                    </div>
                                    <div class="footer-widget__phone-text">
                                        <a href="tel:9200368090">{{companyInfo.phone}}</a>
                                        <p>欢迎拨打</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="site-footer__bottom">
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="site-footer__bottom-inner">
                                <p class="site-footer__bottom-text">{{companyInfo.copyright}} <a target="_blank"
                                        href="https://beian.miit.gov.cn/#/Integrated/index">{{companyInfo.icpNumber}}</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        <!--Site Footer End-->


    </div><!-- /.page-wrapper -->


    <div class="mobile-nav__wrapper" :class="{expanded: expanded}">
        <div class="mobile-nav__overlay mobile-nav__toggler"></div>
        <!-- /.mobile-nav__overlay -->
        <div class="mobile-nav__content">
            <span class="mobile-nav__close mobile-nav__toggler" @click="expanded = false"><i class="fa fa-times"></i></span>

            <div class="logo-box">
                <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143"
                        alt=""></a>
            </div>
            <!-- /.logo-box -->
            <div class="mobile-nav__container"></div>
            <!-- /.mobile-nav__container -->



        </div>
        <!-- /.mobile-nav__content -->
    </div>
    </div>
    <!-- /.mobile-nav__wrapper -->

    <div class="search-popup">
        <div class="search-popup__overlay search-toggler"></div>
        <!-- /.search-popup__overlay -->
        <div class="search-popup__content">
            <form action="#">
                <label for="search" class="sr-only">search here</label><!-- /.sr-only -->
                <input type="text" id="search" placeholder="Search Here...">
                <button type="submit" aria-label="search submit" class="thm-btn">
                    <i class="icon-magnifying-glass"></i>
                </button>
            </form>
        </div>
        <!-- /.search-popup__content -->
    </div>
    <!-- /.search-popup -->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>


    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/jarallax.min.js"></script>
    <script src="static/js/jquery.ajaxchimp.min.js"></script>
    <script src="static/js/jquery.appear.min.js"></script>
    <script src="static/js/jquery.circle-progress.min.js"></script>
    <script src="static/js/jquery.magnific-popup.min.js"></script>
    <script src="static/js/jquery.validate.min.js"></script>
    <script src="static/js/nouislider.min.js"></script>
    <script src="static/js/odometer.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/tiny-slider.min.js"></script>
    <script src="static/js/wNumb.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/isotope.js"></script>
    <script src="static/js/countdown.min.js"></script>
    <script src="static/js/owl.carousel.min.js"></script>
    <script src="static/js/jquery.bxslider.min.js"></script>
    <script src="static/js/bootstrap-select.min.js"></script>
    <script src="static/js/vegas.min.js"></script>
    <script src="static/js/jquery-ui.js"></script>
    <script src="static/js/timePicker.js"></script>
    <script src="static/js/jquery.circleType.js"></script>
    <script src="static/js/jquery.lettering.min.js"></script>




    <!-- template js -->
    <script src="static/js/insur.js"></script>

    <script src="static/js/vue.js"></script>
    <script>
      //const jeeApi = "https://www.taiyibx.com";
      const jeeApi = "http://*************:8080";
        new Vue({
            el: '#app',
            data: {
                expanded: false,
                bannerList: [],
                newsList: [],
                businessPartner: [],
                recommendList: [],
                companyInfo: {},
                keyword: '',
                imgUrl: jeeApi + '/jeecg-boot/'
            },
            mounted() {
                // this.getBanner()
                this.getNews()
                this.getBusinessPartner()
                this.getRecommend()
                this.getCompanyInfo()
            },
            methods: {
                // 获取轮播
                async getBanner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whCarousel/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.bannerList = data.result.records
                    this.$nextTick(() => {
                        window.thmSwiperInit();
                    })

                },
                async toDetail(item) {
                    await fetch(jeeApi + '/jeecg-boot/firmInformation/whClick/addOne?clicksId='+item.clicksId, {
                        method: 'get',
                    })
                    window.location.href = `news-details.html?id=${item.id}`
                },
                // 获取新闻
                async getNews() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whNews/list?name='+this.keyword, {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.newsList = data.result.records

                },
                // 合作伙伴
                async getBusinessPartner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/firmInformation/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.businessPartner = data.result.records

                },
                // 推荐
                async getRecommend() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whProducts/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.recommendList = data.result.records

                },
                // 推荐
                async getCompanyInfo() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.companyInfo = data.result
                    document.title =  this.companyInfo.name

                },
            }
        })
    </script>
</body>

</html>
