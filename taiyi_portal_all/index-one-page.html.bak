<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{companyInfo.name}}</title>
    <meta name="description" content="Insur HTML 5 Template ">

    <!-- 只保留必要的字体引用 -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="static/css/css2.css" rel="stylesheet">

    <!-- 使用合并后的CSS文件 -->
    <link rel="stylesheet" href="static/css/main-min.css">

    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->
    <div id="app">
        <div class="page-wrapper">
            <header class="main-header clearfix">
                <div class="main-header__top">
                    <div class="container">
                        <div class="main-header__top-inner">
                            <div class="main-header__top-address">
                                <ul class="list-unstyled main-header__top-address-list">
                                    <li>
                                        <i class="icon">
                                            <span class="icon-pin"></span>
                                        </i>
                                        <div class="text">
                                            <p>{{companyInfo.address}}</p>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon">
                                            <span class="icon-email"></span>
                                        </i>
                                        <div class="text">
                                            <p><a href="mailto:<EMAIL>">{{companyInfo.mailbox}}</a></p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <nav class="main-menu clearfix">
                    <div class="main-menu__wrapper clearfix">
                        <div class="container">
                            <div class="main-menu__wrapper-inner clearfix">
                                <div class="main-menu__left">
                                    <div class="main-menu__logo">
                                        <a><img :src="imgUrl + companyInfo.logoImg" alt="" style="height: 27px"></a>
                                    </div>
                                    <div class="main-menu__main-menu-box">
                                        <div class="main-menu__main-menu-box-inner">
                                            <a href="#" class="mobile-nav__toggler" @click="expanded = true"><i class="fa fa-bars"></i></a>
                                            <ul class="main-menu__list one-page-scroll-menu">
                                                <li class=" current megamenu scrollToLink">
                                                    <a href="index-one-page.html">首页 </a>
                                                </li>

                                                <li class="scrollToLink">
                                                    <a href="company.html">保险公司 </a>
                                                </li>
                                                <li class="scrollToLink">
                                                    <a href="products.html">保险产品</a>
                                                </li>
                                                <li class="scrollToLink">
                                                    <a href="news.html">新闻资讯</a>
                                                </li>
                                                <li class="scrollToLink" style="margin-right: 37px;">
                                                    <a href="about.html">关于我们</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>

            <div class="stricky-header stricked-menu main-menu">
                <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
            </div><!-- /.stricky-header -->

            <!--Main Slider Start-->
            <section class="main-slider clearfix" id="home">
                <div class="swiper-container thm-swiper__slider" style="width: 1200px" data-swiper-options='{"slidesPerView": 1, "loop": true,
                "effect": "fade",
                "pagination": {
                "el": "#main-slider-pagination",
                "type": "bullets",
                "clickable": true
                },
                "navigation": {
                "nextEl": "#main-slider__swiper-button-next",
                "prevEl": "#main-slider__swiper-button-prev"
                },
                "autoplay": {
                "delay": 5000
                }}'>
                    <div class="swiper-wrapper" v-if="bannerList.length" style="height: 600px">

                        <div class="swiper-slide" v-for="(item,index) in bannerList" :key="index">
                            <div class="image-layer" :style="{'background-image': `url(${imgUrl + item.img})`}"></div>
                            <!-- /.image-layer -->

                            <!-- <div class="main-slider-shape-1 float-bob-x">
                                <img src="static/picture/main-slider-shape-1.png" alt="">
                            </div> -->

                            <div class="container">
                                <div class="row">
                                    <div class="col-xl-12">
                                        <div class="main-slider__content">
                                            <h2 class="main-slider__title">{{item.name}}</h2>
                                            <!-- <p class="main-slider__text">Phasellus condimentum laoreet turpis, ut tincid
                                            sodales <br> in. Integer leo arcu, mollis sit amet tempor.</p>
                                        <div class="main-slider__btn-box">
                                            <a href="about.html" class="thm-btn main-slider__btn">Let's Get Started</a>
                                        </div> -->
                                        <div class="main-slider__btn-box" v-if="item.carouselUrl">
											<a @click="handleJump(item.carouselUrl,item)" class="thm-btn main-slider__btn">查看详情</a>
										</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- If we need navigation buttons -->
                    <div class="main-slider__nav">
                        <div class="swiper-button-prev" id="main-slider__swiper-button-next">
                            <i class="icon-right-arrow"></i>
                        </div>
                        <div class="swiper-button-next" id="main-slider__swiper-button-prev">
                            <i class="icon-right-arrow1"></i>
                        </div>
                    </div>

                </div>
            </section>
            <!--Main Slider End-->

            <!--Feature One Start-->
            <section class="feature-one">
                <div class="container">
                    <div class="feature-one__inner">
                        <div class="row">
                            <!--Feature One Single Start-->
                            <div class="col-xl-4 col-lg-4 wow fadeInUp"  data-wow-delay="100ms">
                                <div class="feature-one__single">
                                    <div class="feature-one__single-inner">
                                        <div class="feature-one__icon">
                                            <span class="icon-insurance"></span>
                                        </div>
                                        <div class="feature-one__count"></div>
                                        <div class="feature-one__shape">
                                            <img src="static/picture/feature-one-shape-1.png" alt="">
                                        </div>
                                        <h3 class="feature-one__title"><a href="about.html">{{sortList.numOneTop}}</a></h3>
                                        <p class="feature-one__text">{{sortList.numOne}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--Feature One Single End-->
                            <!--Feature One Single Start-->
                            <div class="col-xl-4 col-lg-4 wow fadeInUp" data-wow-delay="200ms">
                                <div class="feature-one__single">
                                    <div class="feature-one__single-inner">
                                        <div class="feature-one__icon">
                                            <span class="icon-cashback"></span>
                                        </div>
                                        <div class="feature-one__count"></div>
                                        <div class="feature-one__shape">
                                            <img src="static/picture/feature-one-shape-1.png" alt="">
                                        </div>
                                        <h3 class="feature-one__title"><a href="about.html">{{sortList.numTwoTop}}</a></h3>
                                        <p class="feature-one__text">{{sortList.numTwo}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-4 wow fadeInUp" data-wow-delay="300ms">
                                <div class="feature-one__single">
                                    <div class="feature-one__single-inner">
                                        <div class="feature-one__icon">
                                            <span class="icon-house"></span>
                                        </div>
                                        <div class="feature-one__count"></div>
                                        <div class="feature-one__shape">
                                            <img src="static/picture/feature-one-shape-1.png" alt="">
                                        </div>
                                        <h3 class="feature-one__title"><a href="about.html">{{sortList.numThreeTop}}</a></h3>
                                        <p class="feature-one__text">{{sortList.numThree}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--Feature One Single End-->
                        </div>
                    </div>
                </div>
            </section>
            <!--Feature One End-->


            <!--About One Start-->
            <section class="about-one">
                <div class="about-one-bg wow slideInRight" data-wow-delay="100ms" data-wow-duration="2500ms"
                    style="background-image: url(assets/images/backgrounds/about-one-bg.png);"></div>
                <div class="container">
                    <div class="row">
                        <div class="col-xl-6">
                            <div class="about-one__left">
                                <div class="about-one__img-box wow slideInLeft" data-wow-delay="100ms"
                                    data-wow-duration="2500ms">
                                    <div class="about-one__img">
                                        <img :src="imgUrl + companyInfo.moveImg" alt="">
                                    </div>
                                    <div class="about-one__img-two">
                                        <img style="height: 350px; " :src="imgUrl + companyInfo.homeImg" alt="">
                                    </div>
                                    <div class="about-one__experience">
                                        <h2 class="about-one__experience-year">10</h2>
                                        <p class="about-one__experience-text">多年 <br> 专业服务</p>
                                    </div>
                                    <div class="about-one__shape-1">
                                        <img src="static/picture/about-one-shape-1.jpg" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6">
                            <div class="about-one__right" style="margin-left: 40px;">
                                <div class="section-title text-left">
                                    <div class="section-sub-title-box" style="margin-left: 40px;">
                                        <p class="section-sub-title">关于{{companyInfo.companyName}}</p>
                                        <div class="section-title-shape-1">
                                            <img src="static/picture/section-title-shape-1.png" alt="">
                                        </div>
                                        <div class="section-title-shape-2">
                                            <img src="static/picture/section-title-shape-2.png" alt="">
                                        </div>
                                    </div>
                                    <h2 class="section-title__title">{{companyInfo.name}}简介</h2>
                                </div>
                                <p class="about-one__text-1">更专业的文化传播公司</p>
                                <ul class="list-unstyled about-one__points">
                                    <li>
                                        <div class="icon">
                                            <i class="fa fa-check"></i>
                                        </div>
                                        <div class="text">
                                            <p>更优惠点价格</p>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="icon">
                                            <i class="fa fa-check"></i>
                                        </div>
                                        <div class="text">
                                            <p>更专业的服务</p>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="icon">
                                            <i class="fa fa-check"></i>
                                        </div>
                                        <div class="text">
                                            <p>更全面的保障</p>
                                        </div>
                                    </li>
                                </ul>
                                <p class="about-one__text-2" v-html="companyInfo.introduction"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!--About One End-->

            <!--About One End-->

            <!--Services One Start-->
            <section class="services-one">
                <div class="services-one__top">
                    <div class="container">
                        <div class="section-title text-center">
                            <div class="section-sub-title-box" style="margin-left: 0;">
                                <p class="section-sub-title">合作机构</p>
                                <div class="section-title-shape-1">
                                    <img src="static/picture/section-title-shape-1.png" alt="">
                                </div>
                                <div class="section-title-shape-2">
                                    <img src="static/picture/section-title-shape-2.png" alt="">
                                </div>
                            </div>
                            <h2 class="section-title__title">合作机构</h2>
                        </div>
                    </div>
                </div>
                <div class="services-one__bottom">
                    <div class="services-one__container">
                        <div class="row">
                            <!--Services One Single Start-->
                            <div class="col-xl-3 col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="100ms"
                                v-for="item in businessPartner" :key="item"  @click="handleJump(item.firmUrl,item)">
                                <div class="services-one__single">
                                    <div class="service-one__img">
                                        <img :src="imgUrl + item.img" alt="">
                                    </div>
                                    <div class="service-one__content">
                                        <h2 class="service-one__title" style="height: 60px"><a target="_blank"
                                                :href="item.firmUrl">{{item.name}}</a></h2>
                                        <p class="service-one__text">成立时间：{{item.registrationYear}}</p>
                                        <p class="service-one__text"><i
                                            class="far fa-eye" style="color: #015fc9"></i> {{item.clicksNum}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--Services One Single End-->
                        </div>
                    </div>
                </div>
            </section>
            <!--Services One End-->

            <!--Services One Start-->
            <section class="services-one">
                <div class="services-one__top">
                    <div class="container">
                        <div class="section-title text-center">
                            <div class="section-sub-title-box" style="margin-left: 0;">
                                <p class="section-sub-title">为您推荐</p>
                                <div class="section-title-shape-1">
                                    <img src="static/picture/section-title-shape-1.png" alt="">
                                </div>
                                <div class="section-title-shape-2">
                                    <img src="static/picture/section-title-shape-2.png" alt="">
                                </div>
                            </div>
                            <h2 class="section-title__title">为您推荐</h2>
                        </div>
                    </div>
                </div>
                <div class="services-one__bottom">
                    <div class="services-one__container">
                        <div class="row">
                            <!--Services One Single Start-->
                            <div class="col-xl-3 col-lg-4 col-md-6 wow fadeInUp" data-wow-delay="100ms"
                                v-for="item in recommendList" :key="item" @click="handleJump(item.productsUrl,item)">
                                <div class="services-one__single">
                                    <div class="service-one__img">
                                        <img :src="imgUrl + item.productsImg" alt="">
                                    </div>
                                    <div class="service-one__content" style="height: 186px">
                                        <h2 class="service-one__title ellipsis-2"><a class="ellipsis-2" target="_blank"
                                                :href="item.firmUrl">{{item.name}}</a></h2>
                                        <p class="service-one__text ellipsis-2">{{item.introduction}}</p>
                                        <p class="service-one__text"><i
                                            class="far fa-eye" style="color: #015fc9"></i> {{item.clicksNum}}</p>
                                    </div>
                                </div>
                            </div>
                            <!--Services One Single End-->
                        </div>
                    </div>
                </div>
            </section>
            <!--Services One End-->

            <!--Why Choose One Start-->
            <section class="why-choose-one">
                <div class="why-choose-one-shape-1"
                    style="background-image: url(assets/images/shapes/why-choose-one-shape-1.png);"></div>
                <div class="why-choose-one-shape-2 float-bob-y">
                    <img src="static/picture/why-choose-one-shape-2.png" alt="">
                </div>
                <div class="why-choose-one-shape-3 float-bob-x">
                    <img src="static/picture/why-choose-one-shape-3.png" alt="">
                </div>
                <div class="why-choose-one-shape-4 float-bob-y">
                    <img src="static/picture/why-choose-one-shape-4.png" alt="">
                </div>
                <div class="why-choose-one-shape-5 float-bob-y">
                    <img src="static/picture/why-choose-one-shape-5.png" alt="">
                </div>
                <div class="why-choose-one-shape-6 float-bob-x">
                    <img src="static/picture/why-choose-one-shape-6.png" alt="">
                </div>
                <div class="why-choose-one-img wow slideInRight" data-wow-delay="100ms" data-wow-duration="2500ms">
                    <img src="static/picture/why-choose-one-img.png" alt="">
                </div>
                <div class="container">
                    <div class="row">
                        <div class="col-xl-6 col-lg-7">
                            <div class="why-choose-one__left">
                                <div class="section-title text-left">
                                    <div class="section-sub-title-box">
                                        <p class="section-sub-title">为什么选择{{companyInfo.companyName}}</p>
                                        <div class="section-title-shape-1">
                                            <img src="static/picture/section-title-shape-3.png" alt="">
                                        </div>
                                        <!-- <div class="section-title-shape-2">
                                            <img src="static/picture/section-title-shape-4.png" alt="">
                                        </div> -->
                                    </div>
                                    <h2 class="section-title__title">专业服务创造价值</h2>
                                </div>
                                <p class="why-choose-one__text">以客为本,想客之所想,想客之未想。真诚待客,视客如友,诚信为本,卓越服务</p>
                                <div class="why-choose-one__list-box">
                                    <ul class="list-unstyled why-choose-one__list">
                                        <li>
                                            <div class="why-choose-one__single">
                                                <div class="why-choose-one__list-icon">
                                                    <span class="icon-easy-to-use"></span>
                                                </div>
                                                <div class="why-choose-one__list-title-box">
                                                    <div class="why-choose-one__list-title-inner">
                                                        <h3 class="why-choose-one__list-title">更快的流程</h3>
                                                    </div>
                                                    <!-- <div class="why-choose-one__list-text-box">
                                                        <p class="why-choose-one__list-text">Lorem ipsum dolor sit amet,
                                                            sectetur adipiscing elit.</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="why-choose-one__single">
                                                <div class="why-choose-one__list-icon">
                                                    <span class="icon-contract"></span>
                                                </div>
                                                <div class="why-choose-one__list-title-box">
                                                    <div class="why-choose-one__list-title-inner">
                                                        <h3 class="why-choose-one__list-title">更完善的保障</h3>
                                                    </div>
                                                    <!-- <div class="why-choose-one__list-text-box">
                                                        <p class="why-choose-one__list-text">Lorem ipsum dolor sit amet,
                                                            sectetur adipiscing elit.</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="why-choose-one__single">
                                                <div class="why-choose-one__list-icon">
                                                    <span class="icon-policy"></span>
                                                </div>
                                                <div class="why-choose-one__list-title-box">
                                                    <div class="why-choose-one__list-title-inner">
                                                        <h3 class="why-choose-one__list-title">更好的服务</h3>
                                                    </div>
                                                    <!-- <div class="why-choose-one__list-text-box">
                                                        <p class="why-choose-one__list-text">Lorem ipsum dolor sit amet,
                                                            sectetur adipiscing elit.</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="why-choose-one__single">
                                                <div class="why-choose-one__list-icon">
                                                    <span class="icon-fund"></span>
                                                </div>
                                                <div class="why-choose-one__list-title-box">
                                                    <div class="why-choose-one__list-title-inner">
                                                        <h3 class="why-choose-one__list-title">更透明的价格</h3>
                                                    </div>
                                                    <!-- <div class="why-choose-one__list-text-box">
                                                        <p class="why-choose-one__list-text">Lorem ipsum dolor sit amet,
                                                            sectetur adipiscing elit.</p>
                                                    </div> -->
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!--Why Choose One End-->

            <!--News One Start-->
            <section class="news-one" id="news">
                <div class="container">
                    <div class="section-title text-center">
                        <div class="section-sub-title-box">
                            <p class="section-sub-title">新闻资讯</p>
                            <div class="section-title-shape-1">
                                <img src="static/picture/section-title-shape-1.png" alt="">
                            </div>
                            <div class="section-title-shape-2">
                                <img src="static/picture/section-title-shape-2.png" alt="">
                            </div>
                        </div>
                        <h2 class="section-title__title">{{companyInfo.companyName}}动态</h2>
                    </div>
                    <div class="row">
                        <!--News One Single Start-->
                        <div class="col-xl-4 col-lg-4 wow fadeInUp" data-wow-delay="100ms"
                            v-for="(item,index) in newsList" @click="toDetail(item)" :key="index">
                            <div class="news-one__single">
                                <div class="news-one__img">
                                    <img :src="imgUrl + item.newsImg" alt="">
                                    <!-- <div class="news-one__tag">
                                    <p><i class="far fa-folder"></i>BUSINESS</p>
                                </div> -->
                                    <div class="news-one__arrow-box">
                                        <a class="news-one__arrow">
                                            <span class="icon-right-arrow1"></span>
                                        </a>
                                    </div>
                                </div>
                                <div class="news-one__content">
                                    <ul class="list-unstyled news-one__meta">
                                        <li><a><i
                                                    class="far fa-calendar"></i>{{item.messageTiming}}</a>
                                        </li>
                                        <li><a><i
                                                    class="far fa-eye" style="color: #015fc9"></i>{{item.clicksNum}}</a>
                                        </li>
                                    </ul>
                                    <h3 class="news-one__title"><a>{{item.name}}</a></h3>
                                    <div class="news-one__read-more">
                                        <a>详情 <i class="fas fa-angle-double-right"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </section>

            <!--News One End-->


            <!--Tracking Start-->
            <section class="tracking">
                <div class="container">
                    <div class="tracking__inner">
                        <div class="tracking-shape-1 float-bob-y">
                            <img src="static/picture/tracking-shape-1.png" alt="">
                        </div>
                        <div class="tracking-shape-2 float-bob-x">
                            <img src="static/picture/tracking-shape-2.png" alt="">
                        </div>
                        <div class="tracking-shape-3 float-bob-x">
                            <img src="static/picture/tracking-shape-3.png" alt="">
                        </div>
                        <div class="tracking-shape-4 float-bob-y">
                            <img src="static/picture/tracking-shape-4.png" alt="">
                        </div>
                        <div class="tracking__left">
                            <div class="tracking__icon">
                                <span class="icon-folder"></span>
                            </div>
                            <div class="tracking__content">
                                <p class="tracking__sub-title">为您和家人上一份保险</p>
                                <h3 class="tracking__title">立刻获取报价</h3>
                            </div>
                        </div>
                        <div class="tracking__btn-box">
                            <a class="thm-btn tracking__btn">{{companyInfo.phone}}</a>
                        </div>
                    </div>
                </div>
            </section>
            <!--Tracking End-->

            <!--Site Footer Start-->
            <footer class="site-footer">
                <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
                </div>
                <div class="container">
                    <div class="site-footer__top">
                        <div class="row">
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="100ms">
                                <div class="footer-widget__column footer-widget__about">
                                    <div class="footer-widget__logo">
                                        <a><img :src="imgUrl + companyInfo.logoImg" alt="" style="height: 34px"></a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="200ms">
                                <div class="footer-widget__column footer-widget__contact clearfix">
                                    <h3 class="footer-widget__title">公司地址</h3>
                                    <ul class="footer-widget__contact-list list-unstyled clearfix">
                                        <li>
                                            <div class="icon">
                                                <span class="icon-pin"></span>
                                            </div>
                                            <div class="text">
                                                <p>{{companyInfo.address}}</p>
                                            </div>
                                        </li>
                                    </ul>
                                    <div class="footer-widget__open-hour">
                                        <h3 class="footer-widget__open-hour-title">工作时间</h3>
                                        <h3 class="footer-widget__open-hour-text">工作日 早上9:00-18:00</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="400ms">
                                <div class="footer-widget__column footer-widget__newsletter">
                                    <h3 class="footer-widget__title">联系电话</h3>
                                    <div class="footer-widget__phone">
                                        <div class="footer-widget__phone-icon">
                                            <span class="icon-telephone"></span>
                                        </div>
                                        <div class="footer-widget__phone-text">
                                            <a href="tel:9200368090">{{companyInfo.phone}}</a>
                                            <p>欢迎拨打</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="site-footer__bottom">
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="site-footer__bottom-inner">
                                    <p class="site-footer__bottom-text">{{companyInfo.copyright}} <a target="_blank"
                                            href="https://beian.miit.gov.cn/#/Integrated/index">{{companyInfo.icpNumber}}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!--Site Footer End-->


        </div><!-- /.page-wrapper -->


        <div class="mobile-nav__wrapper" :class="{expanded: expanded}">
            <div class="mobile-nav__overlay mobile-nav__toggler"></div>
            <!-- /.mobile-nav__overlay -->
            <div class="mobile-nav__content">
                <span class="mobile-nav__close mobile-nav__toggler" @click="expanded = false"><i class="fa fa-times"></i></span>

                <div class="logo-box">
                    <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143"
                            alt=""></a>
                </div>
                <!-- /.logo-box -->
                <div class="mobile-nav__container"></div>
                <!-- /.mobile-nav__container -->



            </div>
            <!-- /.mobile-nav__content -->
        </div>
    </div>
    <!-- /.mobile-nav__wrapper -->

    <div class="search-popup">
        <div class="search-popup__overlay search-toggler"></div>
        <!-- /.search-popup__overlay -->
        <div class="search-popup__content">
            <form action="#">
                <label for="search" class="sr-only">search here</label><!-- /.sr-only -->
                <input type="text" id="search" placeholder="Search Here...">
                <button type="submit" aria-label="search submit" class="thm-btn">
                    <i class="icon-magnifying-glass"></i>
                </button>
            </form>
        </div>
        <!-- /.search-popup__content -->
    </div>
    <!-- /.search-popup -->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>

    <!-- 核心JS库 -->
    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/owl.carousel.min.js"></script>
    
    <!-- 模板JS -->
    <script src="static/js/insur-min.js"></script>
    
    <!-- Vue框架 -->
    <script src="static/js/vue.js"></script>
    <script>
        const jeeApi = "https://www.taiyibx.com";
        new Vue({
            el: '#app',
            data: {
                expanded: false,
                bannerList: [],
                newsList: [],
                businessPartner: [],
                recommendList: [],
                sortList: [],
                companyInfo: {},
                imgUrl: jeeApi + '/jeecg-boot/',
                // 添加缓存状态变量
                cacheTime: 0,
                cacheExpireTime: 30 * 60 * 1000 // 缓存30分钟
            },
            mounted() {
                // 检查是否有缓存
                this.loadFromCache();
                
                // 如果没有缓存或缓存已过期,则重新加载数据
                if (Date.now() - this.cacheTime > this.cacheExpireTime) {
                    this.loadAllData();
                }
            },
            methods: {
                // 加载所有数据并缓存
                loadAllData() {
                    Promise.all([
                        this.getBanner(),
                        this.getNews(),
                        this.getBusinessPartner(),
                        this.getRecommend(),
                        this.getSortList(),
                        this.getCompanyInfo()
                    ]).then(() => {
                        // 将数据保存到本地缓存
                        this.saveToCache();
                    });
                },
                
                // 保存数据到本地缓存
                saveToCache() {
                    const cacheData = {
                        bannerList: this.bannerList,
                        newsList: this.newsList,
                        businessPartner: this.businessPartner,
                        recommendList: this.recommendList,
                        sortList: this.sortList,
                        companyInfo: this.companyInfo,
                        cacheTime: Date.now()
                    };
                    localStorage.setItem('taiyi_web_cache', JSON.stringify(cacheData));
                },
                
                // 从本地缓存加载数据
                loadFromCache() {
                    const cacheDataStr = localStorage.getItem('taiyi_web_cache');
                    if (cacheDataStr) {
                        try {
                            const cacheData = JSON.parse(cacheDataStr);
                            this.bannerList = cacheData.bannerList;
                            this.newsList = cacheData.newsList;
                            this.businessPartner = cacheData.businessPartner;
                            this.recommendList = cacheData.recommendList;
                            this.sortList = cacheData.sortList;
                            this.companyInfo = cacheData.companyInfo;
                            this.cacheTime = cacheData.cacheTime;
                            
                            // 如果有缓存的数据,初始化轮播图
                            if (this.bannerList && this.bannerList.length) {
                                this.$nextTick(() => {
                                    window.thmSwiperInit();
                                });
                            }
                            
                            // 设置页面标题
                            if (this.companyInfo && this.companyInfo.name) {
                                document.title = this.companyInfo.name;
                            }
                        } catch (error) {
                            console.error('缓存数据解析错误', error);
                            this.cacheTime = 0;
                        }
                    }
                },
                
                // 获取轮播
                async getBanner() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whCarousel/list', {
                            method: 'get',
                        });
                        const data = await response.json();
                        if (data && data.result && data.result.records) {
                            this.bannerList = data.result.records;
                            this.$nextTick(() => {
                                window.thmSwiperInit();
                            });
                        }
                    } catch (error) {
                        console.error('获取轮播图失败', error);
                    }
                },
                
                // 获取新闻
                async getNews() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whNews/list', {
                            method: 'get',
                        });
                        const data = await response.json();
                        if (data && data.result && data.result.records) {
                            this.newsList = data.result.records;
                        }
                    } catch (error) {
                        console.error('获取新闻失败', error);
                    }
                },
                
                async toDetail(item) {
                    try {
                        await fetch(jeeApi + '/jeecg-boot/firmInformation/whClick/addOne?clicksId='+item.clicksId, {
                            method: 'get',
                        });
                    } catch (error) {
                        console.error('更新点击量失败', error);
                    }
                    window.location.href = `news-details.html?id=${item.id}`;
                },
                
                // 合作伙伴
                async getBusinessPartner() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/firmInformation/list', {
                            method: 'get',
                        });
                        const data = await response.json();
                        if (data && data.result && data.result.records) {
                            this.businessPartner = data.result.records;
                        }
                    } catch (error) {
                        console.error('获取合作伙伴失败', error);
                    }
                },
                
                // 门户标签
                async getSortList() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/api/excel/whIntroduction/queryOne', {
                            method: 'get',
                        });
                        const data = await response.json();
                        if (data && data.result) {
                            this.sortList = data.result;
                        }
                    } catch (error) {
                        console.error('获取标签失败', error);
                    }
                },
                
                // 推荐
                async getRecommend() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whProducts/list', {
                            method: 'get',
                        });
                        const data = await response.json();
                        if (data && data.result && data.result.records) {
                            this.recommendList = data.result.records;
                        }
                    } catch (error) {
                        console.error('获取推荐失败', error);
                    }
                },
                
                // 公司信息
                async getCompanyInfo() {
                    try {
                        const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                            method: 'get',
                        });
                        const data = await response.json();
                        if (data && data.result) {
                            this.companyInfo = data.result;
                            document.title = this.companyInfo.name;
                        }
                    } catch (error) {
                        console.error('获取公司信息失败', error);
                    }
                },
                
                // 跳转
                handleJump(url, item) {
                    try {
                        fetch(jeeApi + '/jeecg-boot/firmInformation/whClick/addOne?clicksId='+item.clicksId, {
                            method: 'get',
                        });
                    } catch (error) {
                        console.error('更新点击量失败', error);
                    }
                    window.open(url);
                }   
            }
        });
    </script>
</body>

</html>
