(function ($) {
  "use strict";
  
  // 轮播图初始化函数
  function thmSwiperInit() {
    // swiper slider
    if ($(".thm-swiper__slider").length) {
      $(".thm-swiper__slider").each(function () {
        let elm = $(this);
        let options = elm.data("swiper-options");
        let thmSwiperSlider = new Swiper(elm, options);
      });
    }
  }
  
  // 将函数暴露到全局
  window.thmSwiperInit = thmSwiperInit;

  // 图片懒加载初始化
  function initLazyLoading() {
    const lazyImages = document.querySelectorAll('img.lazy');
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
          if (entry.isIntersecting) {
            const lazyImage = entry.target;
            if (lazyImage.dataset.src) {
              lazyImage.src = lazyImage.dataset.src;
              lazyImage.removeAttribute('data-src');
            }
            if (lazyImage.dataset.srcset) {
              lazyImage.srcset = lazyImage.dataset.srcset;
              lazyImage.removeAttribute('data-srcset');
            }
            lazyImage.classList.add('loaded');
            imageObserver.unobserve(lazyImage);
          }
        });
      }, {
        rootMargin: '0px 0px 200px 0px' // 提前200px开始加载
      });
      
      lazyImages.forEach(function(lazyImage) {
        imageObserver.observe(lazyImage);
      });
    } else {
      // 回退方案 - 简单的scroll事件处理
      let lazyImageTimeout;
      
      function lazyLoadImages() {
        if (lazyImageTimeout) {
          clearTimeout(lazyImageTimeout);
        }
        
        lazyImageTimeout = setTimeout(function() {
          const scrollTop = window.pageYOffset;
          
          lazyImages.forEach(function(lazyImage) {
            if (lazyImage.offsetTop < (window.innerHeight + scrollTop + 200)) {
              if (lazyImage.dataset.src) {
                lazyImage.src = lazyImage.dataset.src;
                lazyImage.removeAttribute('data-src');
              }
              if (lazyImage.dataset.srcset) {
                lazyImage.srcset = lazyImage.dataset.srcset;
                lazyImage.removeAttribute('data-srcset');
              }
              lazyImage.classList.add('loaded');
            }
          });
          
          if (document.querySelectorAll('img.lazy:not(.loaded)').length === 0) {
            document.removeEventListener('scroll', lazyLoadImages);
            window.removeEventListener('resize', lazyLoadImages);
            window.removeEventListener('orientationChange', lazyLoadImages);
          }
        }, 20);
      }
      
      document.addEventListener('scroll', lazyLoadImages);
      window.addEventListener('resize', lazyLoadImages);
      window.addEventListener('orientationChange', lazyLoadImages);
      
      // 初始触发一次
      lazyLoadImages();
    }
  }

  // 鼠标指针效果
  if ($(".custom-cursor").length) {
    var cursor = document.querySelector(".custom-cursor__cursor");
    var cursorinner = document.querySelector(".custom-cursor__cursor-two");
    var a = document.querySelectorAll("a");

    document.addEventListener("mousemove", function (e) {
      var x = e.clientX;
      var y = e.clientY;
      cursor.style.transform = `translate3d(calc(${e.clientX}px - 50%), calc(${e.clientY}px - 50%), 0)`;
    });

    document.addEventListener("mousemove", function (e) {
      var x = e.clientX;
      var y = e.clientY;
      cursorinner.style.left = x + "px";
      cursorinner.style.top = y + "px";
    });

    document.addEventListener("mousedown", function () {
      cursor.classList.add("click");
      cursorinner.classList.add("custom-cursor__innerhover");
    });

    document.addEventListener("mouseup", function () {
      cursor.classList.remove("click");
      cursorinner.classList.remove("custom-cursor__innerhover");
    });

    a.forEach((item) => {
      item.addEventListener("mouseover", () => {
        cursor.classList.add("custom-cursor__hover");
      });
      item.addEventListener("mouseleave", () => {
        cursor.classList.remove("custom-cursor__hover");
      });
    });
  }

  // 页面滚动到顶部按钮
  if ($(".scroll-to-target").length) {
    $(".scroll-to-target").on("click", function () {
      var target = $(this).attr("data-target");
      // 动画滚动到目标位置
      $("html, body").animate({
        scrollTop: $(target).offset().top
      }, 1000);
      return false;
    });
  }

  // 预加载动画
  if ($(".preloader").length) {
    $(".preloader").fadeOut();
  }

  // 页面准备完成后初始化
  $(window).on("load", function () {
    if ($(".preloader").length) {
      $(".preloader").fadeOut();
    }
    thmSwiperInit();
    initLazyLoading(); // 初始化懒加载
  });

  // 动画初始化
  if ($(".wow").length) {
    var wow = new WOW({
      boxClass: "wow",
      animateClass: "animated",
      mobile: true,
      live: true
    });
    wow.init();
  }

})(jQuery); 