function ajaxRequest(options) {
    const defaultHeaders = {
        'X-Access-Token': '123',
        'Content-Type': 'application/x-www-form-urlencoded'
    };
    var jeeApi = "http://192.168.22.20:8080";
    const settings = {
        url: jeeApi + options.url,
        type: options.method || 'GET',
        data: options.method === 'POST' ? JSON.stringify(options.data) : options.data,
        // headers: { ...defaultHeaders, ...options.headers },
        success: options.success,
        error: options.error
    };

    $.ajax(settings);
}