$(document).ready(function () {
    
    // $.get(jeeApi + "/jeecg-boot/api/firmInformation/whCarousel/list", function (data) {
    //     console.log(data);
    // }).fail(function (error) {
    //     console.log("请求失败:", error);
    // });
    // console.log(1324)
    // $('#swiperWrapper').attr('tes',1234)
    // $('#swiperWrapper .main-slider-shape-1 img').attr('src', 'https://gips3.baidu.com/it/u=3850307087,2485702220&fm=3028&app=3028&f=PNG&fmt=auto&q=100&size=f544_40');

    $('#swiperWrapper .swiper-slide .main-slider-shape-1 img').each(function() {
        console.log($(this))
        $(this).attr('src', 'https://gimg3.baidu.com/search/src=http%3A%2F%2Fpics0.baidu.com%2Ffeed%2Fa6efce1b9d16fdfa1e78268c218af85a94ee7b54.jpeg%40f_auto%3Ftoken%3Db33666c336cdfa22a86dddc3c634c2d8&refer=http%3A%2F%2Fwww.baidu.com&app=2021&size=f360,240&n=0&g=0n&q=75&fmt=auto?sec=1723568400&t=2d9c300d21e2d9328cea0a309ce884ae');
    });

    ajaxRequest({
        url: "/jeecg-boot/api/firmInformation/whCarousel/list",
        success: (res) => {
            console.log(res)
        },
        error: (res) => {
            
            
        }
    })
});
