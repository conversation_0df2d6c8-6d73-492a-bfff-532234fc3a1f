@font-face {
  font-family: 'insur-two-icon';
  src:  url('fonts/insur-two-icon.eot?5ulvax');
  src:  url('../font/insur-two-icon.eot') format('embedded-opentype'),
    url('../font/insur-two-icon.ttf') format('truetype'),
    url('../font/insur-two-icon.woff') format('woff'),
    url('https://layerdrops.com/insur/main-html/assets/vendors/insur-two-icon/fonts/insur-two-icon.svg?5ulvax') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="insur-two-icon-"], [class*=" insur-two-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'insur-two-icon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.insur-two-icon-shopping-cart:before {
  content: "\e900";
}
