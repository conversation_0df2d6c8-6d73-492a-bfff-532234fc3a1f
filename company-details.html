<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太⼀企业管理有限公司</title>
    <!-- favicons Icons -->




    <meta name="description" content="Insur HTML 5 Template ">

    <!-- fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">

    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">

    <link href="static/css/css2.css" rel="stylesheet">


    <link rel="stylesheet" href="static/css/bootstrap.min.css">
    <link rel="stylesheet" href="static/css/animate.min.css">
    <link rel="stylesheet" href="static/css/custom-animate.css">
    <link rel="stylesheet" href="static/css/all.min.css">
    <link rel="stylesheet" href="static/css/jarallax.css">
    <link rel="stylesheet" href="static/css/jquery.magnific-popup.css">
    <link rel="stylesheet" href="static/css/nouislider.min.css">
    <link rel="stylesheet" href="static/css/nouislider.pips.css">
    <link rel="stylesheet" href="static/css/odometer.min.css">
    <link rel="stylesheet" href="static/css/swiper.min.css">
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="static/css/style1.css">
    <link rel="stylesheet" href="static/css/tiny-slider.min.css">
    <link rel="stylesheet" href="static/css/stylesheet.css">
    <link rel="stylesheet" href="static/css/owl.carousel.min.css">
    <link rel="stylesheet" href="static/css/owl.theme.default.min.css">
    <link rel="stylesheet" href="static/css/jquery.bxslider.css">
    <link rel="stylesheet" href="static/css/bootstrap-select.min.css">
    <link rel="stylesheet" href="static/css/vegas.min.css">
    <link rel="stylesheet" href="static/css/jquery-ui.css">
    <link rel="stylesheet" href="static/css/timePicker.css">

    <!-- template styles -->
    <link rel="stylesheet" href="static/css/insur.css">
    <link rel="stylesheet" href="static/css/insur-responsive.css">

    <!-- 公司详情页样式 -->
    <style>
        .company-profile {
            padding: 30px 0;
        }

        .company-details__card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }

        .company-details__title {
            font-size: 24px;
            color: var(--insur-black);
            margin-bottom: 30px;
            font-weight: 700;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .company-info-table {
            width: 100%;
        }

        .info-row {
            display: flex;
            padding: 12px 0;
            border-bottom: 1px dashed #eee;
        }

        .info-label {
            width: 150px;
            color: #666;
            font-size: 15px;
            flex-shrink: 0;
        }

        .info-value {
            flex-grow: 1;
            color: #333;
            font-size: 15px;
        }

        .info-value a {
            color: var(--insur-base);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .info-value a:hover {
            color: var(--insur-primary);
        }

        .business-scope {
            line-height: 1.8;
        }

        .company-sidebar {
            margin-top: 0;
        }

        .sidebar-box {
            background-color: #f5f9fc;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .sidebar-item {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e4e8;
        }

        .sidebar-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .sidebar-label {
            color: #666;
            font-size: 15px;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .sidebar-value {
            color: #333;
            font-size: 15px;
            word-break: break-all;
        }

        .sidebar-value a {
            color: var(--insur-base);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar-value a:hover {
            color: var(--insur-primary);
        }

        @media (max-width: 1199px) {
            .company-sidebar {
                margin-top: 30px;
            }
        }
    </style>
</head>

<body class="custom-cursor">

    <div class="custom-cursor__cursor"></div>
    <div class="custom-cursor__cursor-two"></div>





    <div class="preloader">
        <div class="preloader__image"></div>
    </div>
    <!-- /.preloader -->

    <div id="app">
        <div class="page-wrapper">
            <header class="main-header clearfix">
                <div class="main-header__top">
                    <div class="container">
                        <div class="main-header__top-inner">
                            <div class="main-header__top-address">
                                <ul class="list-unstyled main-header__top-address-list">
                                    <li>
                                        <i class="icon">
                                            <span class="icon-pin"></span>
                                        </i>
                                        <div class="text">
                                            <p>{{companyInfo.address}}</p>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon">
                                            <span class="icon-email"></span>
                                        </i>
                                        <div class="text">
                                            <p><a href="mailto:<EMAIL>">{{companyInfo.mailbox}}</a></p>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <nav class="main-menu clearfix">
                    <div class="main-menu__wrapper clearfix">
                        <div class="container">
                            <div class="main-menu__wrapper-inner clearfix">
                                <div class="main-menu__left">
                                    <div class="main-menu__logo">
                                        <a><img :src="companyInfo.logoImg" alt="" style="height: 27px"></a>
                                    </div>
                                    <div class="main-menu__main-menu-box">
                                        <div class="main-menu__main-menu-box-inner">
                                            <a href="#" class="mobile-nav__toggler" @click="expanded = true"><i
                                                    class="fa fa-bars"></i></a>
                                            <ul class="main-menu__list one-page-scroll-menu">
                                                <li class=" megamenu scrollToLink">
                                                    <a href="index-one-page.html">首页 </a>
                                                </li>

                                                <li class="scrollToLink current">
                                                    <a href="company.html">保险公司 </a>
                                                </li>
                                                <li class="scrollToLink">
                                                    <a href="products.html">保险产品</a>
                                                </li>
                                                <li class="scrollToLink">
                                                    <a href="news.html">新闻资讯</a>
                                                </li>
                                                <li class="scrollToLink" style="margin-right: 37px;">
                                                    <a href="about.html">关于我们</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </header>

            <div class="stricky-header stricked-menu main-menu">
                <div class="sticky-header__content"></div><!-- /.sticky-header__content -->
            </div><!-- /.stricky-header -->



            <!--Page Header Start-->
            <section class="page-header">
                <div class="page-header-bg" style="background-image: url(assets/images/backgrounds/page-header-bg.jpg)">
                </div>
                <div class="page-header-shape-1"><img src="static/picture/page-header-shape-1.png" alt=""></div>
                <div class="container">
                    <div class="page-header__inner">
                        <!-- <ul class="thm-breadcrumb list-unstyled">
                        <li><a>Home</a></li>
                        <li><span>/</span></li>
                        <li>news</li>
                    </ul> -->
                        <h2>企业信息</h2>
                    </div>
                </div>
            </section>
            <!--Page Header End-->

            <!--Company Details Start-->
            <section class="news-details">
                <div class="container">
                    <div class="company-profile">
                        <div class="row">
                            <div class="col-xl-8">
                                <div class="company-details__card">
                                    <h2 class="company-details__title">{{companyDetail.name}}</h2>

                                    <div class="company-info-table">
                                        <div class="info-row">
                                            <div class="info-label">法人代表：</div>
                                            <div class="info-value">{{companyDetail.legalPerson || '-'}}</div>
                                        </div>

                                        <div class="info-row">
                                            <div class="info-label">注册资本：</div>
                                            <div class="info-value">{{companyDetail.registeredCapital || '-'}}</div>
                                        </div>

                                        <div class="info-row">
                                            <div class="info-label">成立日期：</div>
                                            <div class="info-value">{{companyDetail.regTime || '-'}}</div>
                                        </div>



                                        <div class="info-row">
                                            <div class="info-label">统一社会信用代码：</div>
                                            <div class="info-value">{{companyDetail.creditCode || '-'}}</div>
                                        </div>

                                        <div class="info-row">
                                            <div class="info-label">企业地址：</div>
                                            <div class="info-value">{{companyDetail.companyAddress || '-'}}</div>
                                        </div>

                                        <div class="info-row">
                                            <div class="info-label">企业邮箱：</div>
                                            <div class="info-value">
                                                <a v-if="companyDetail.email" :href="'mailto:' + companyDetail.email">{{companyDetail.email}}</a>
                                                <span v-else>-</span>
                                            </div>
                                        </div>

                                        <div class="info-row">
                                            <div class="info-label">企业简介：</div>
                                            <div class="info-value business-scope">{{companyDetail.intro || '-'}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xl-4">
                                <div class="company-sidebar">
                                    <div class="sidebar-box">
                                        <div class="sidebar-item">
                                            <div class="sidebar-label">网址：</div>
                                            <div class="sidebar-value">
                                                <a v-if="companyDetail.website" :href="companyDetail.website" target="_blank">{{companyDetail.website}}</a>
                                                <span v-else>-</span>
                                            </div>
                                        </div>

                                        <div class="sidebar-item">
                                            <div class="sidebar-label">电话：</div>
                                            <div class="sidebar-value">{{companyDetail.phone || '-'}}</div>
                                        </div>

                                        <div class="sidebar-item">
                                            <div class="sidebar-label">成立日期：</div>
                                            <div class="sidebar-value">{{companyDetail.regTime || '-'}}</div>
                                        </div>

                                        <div class="sidebar-item">
                                            <div class="sidebar-label">查看次数：</div>
                                            <div class="sidebar-value">{{companyDetail.clickNum || '0'}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!--Company Details End-->

            <!--Site Footer Start-->
            <footer class="site-footer">
                <div class="site-footer-bg" style="background-image: url(static/image/site-footer-bg.png);">
                </div>
                <div class="container">
                    <div class="site-footer__top">
                        <div class="row">
<!--                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="100ms">-->
<!--                                <div class="footer-widget__column footer-widget__about">-->
<!--                                    <div class="footer-widget__logo">-->
<!--                                        <a><img :src="companyInfo.logoImg" alt="" style="height: 34px"></a>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="200ms">
                                <div class="footer-widget__column footer-widget__contact clearfix">
                                    <h3 class="footer-widget__title">公司地址</h3>
                                    <ul class="footer-widget__contact-list list-unstyled clearfix">
                                        <li>
                                            <div class="icon">
                                                <span class="icon-pin"></span>
                                            </div>
                                            <div class="text">
                                                <p>{{companyInfo.address}}</p>
                                            </div>
                                        </li>
                                    </ul>
                                    <div class="footer-widget__open-hour">
                                        <h3 class="footer-widget__open-hour-title">工作时间</h3>
                                        <h3 class="footer-widget__open-hour-text">工作日 早上9:00-18:00</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-4 col-lg-8 col-md-8 wow fadeInUp" data-wow-delay="400ms">
                                <div class="footer-widget__column footer-widget__newsletter">
                                    <h3 class="footer-widget__title">联系电话</h3>
                                    <div class="footer-widget__phone">
                                        <div class="footer-widget__phone-icon">
                                            <span class="icon-telephone"></span>
                                        </div>
                                        <div class="footer-widget__phone-text">
                                            <a href="tel:9200368090">{{companyInfo.phone}}</a>
                                            <p>欢迎拨打</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="site-footer__bottom">
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="site-footer__bottom-inner">
                                    <p class="site-footer__bottom-text">{{companyInfo.copyright}} <a target="_blank"
                                            href="https://beian.miit.gov.cn/#/Integrated/index">{{companyInfo.icpNumber}}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <!--Site Footer End-->


        </div><!-- /.page-wrapper -->


        <div class="mobile-nav__wrapper" :class="{expanded: expanded}">
            <div class="mobile-nav__overlay mobile-nav__toggler"></div>
            <!-- /.mobile-nav__overlay -->
            <div class="mobile-nav__content">
                <span class="mobile-nav__close mobile-nav__toggler" @click="expanded = false"><i
                        class="fa fa-times"></i></span>

                <div class="logo-box">
                    <a aria-label="logo image"><img src="static/picture/logo-2.png" width="143"
                            alt=""></a>
                </div>
                <!-- /.logo-box -->
                <div class="mobile-nav__container"></div>
                <!-- /.mobile-nav__container -->



            </div>
            <!-- /.mobile-nav__content -->
        </div>
    </div>
    <!-- /.mobile-nav__wrapper -->

    <div class="search-popup">
        <div class="search-popup__overlay search-toggler"></div>
        <!-- /.search-popup__overlay -->
        <div class="search-popup__content">
            <form action="#">
                <label for="search" class="sr-only">search here</label><!-- /.sr-only -->
                <input type="text" id="search" placeholder="Search Here...">
                <button type="submit" aria-label="search submit" class="thm-btn">
                    <i class="icon-magnifying-glass"></i>
                </button>
            </form>
        </div>
        <!-- /.search-popup__content -->
    </div>
    <!-- /.search-popup -->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>


    <script src="static/js/jquery-3.6.0.min.js"></script>
    <script src="static/js/bootstrap.bundle.min.js"></script>
    <script src="static/js/jarallax.min.js"></script>
    <script src="static/js/jquery.ajaxchimp.min.js"></script>
    <script src="static/js/jquery.appear.min.js"></script>
    <script src="static/js/jquery.circle-progress.min.js"></script>
    <script src="static/js/jquery.magnific-popup.min.js"></script>
    <script src="static/js/jquery.validate.min.js"></script>
    <script src="static/js/nouislider.min.js"></script>
    <script src="static/js/odometer.min.js"></script>
    <script src="static/js/swiper.min.js"></script>
    <script src="static/js/tiny-slider.min.js"></script>
    <script src="static/js/wNumb.min.js"></script>
    <script src="static/js/wow.js"></script>
    <script src="static/js/isotope.js"></script>
    <script src="static/js/countdown.min.js"></script>
    <script src="static/js/owl.carousel.min.js"></script>
    <script src="static/js/jquery.bxslider.min.js"></script>
    <script src="static/js/bootstrap-select.min.js"></script>
    <script src="static/js/vegas.min.js"></script>
    <script src="static/js/jquery-ui.js"></script>
    <script src="static/js/timePicker.js"></script>
    <script src="static/js/jquery.circleType.js"></script>
    <script src="static/js/jquery.lettering.min.js"></script>




    <!-- template js -->
    <script src="static/js/insur.js"></script>

    <script src="static/js/vue.js"></script>
    <script src="static/js/api-config.js"></script>
    <script>
      //const jeeApi = "https://www.taiyibx.com";
      //const jeeApi = "http://*************:8080";
        new Vue({
            el: '#app',
            data: {
                expanded: false,
                bannerList: [],
                newsList: [],
                businessPartner: [],
                recommendList: [],
                companyInfo: {},
                newsDetail: {},
                companyDetail: {},
                keyword: '',
            },
            mounted() {
                // this.getBanner()
                // this.getNews()
                this.getBusinessPartner()
                this.getRecommend()
                this.getCompanyInfo()
                this.getCompanyDetail()
            },
            methods: {
                // 获取轮播
                async getBanner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whCarousel/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.bannerList = data.result.records
                    this.$nextTick(() => {
                        window.thmSwiperInit();
                    })

                },
                // 获取新闻
                async getNews() {
                    // 获取当前URL
                    const url = window.location.href;

                    // 使用URLSearchParams来解析URL中的参数
                    const params = new URLSearchParams(url.split('?')[1]);

                    // 获取id参数的值
                    const id = params.get('id');
                    const response = await fetch(jeeApi + '/jeecg-boot/api/wechat/appNews/queryById?id=' + id, {
                        method: 'get',
                    })
                    const data = await response.json();
                    // this.newsList = data.result.records
                    this.newsDetail = data.result

                },
                // 合作伙伴
                async getBusinessPartner() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/firmInformation/list?name=' + this.keyword, {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.businessPartner = data.result.records

                },
                // 推荐
                async getRecommend() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whProducts/list', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.recommendList = data.result.records

                },
                // 推荐
                async getCompanyInfo() {
                    const response = await fetch(jeeApi + '/jeecg-boot/api/firmInformation/whHome/getOne', {
                        method: 'get',
                    })
                    const data = await response.json();
                    this.companyInfo = data.result
                },

                // 获取公司详情
                async getCompanyDetail() {
                    // 获取当前URL
                    const url = window.location.href;

                    // 使用URLSearchParams来解析URL中的参数
                    const params = new URLSearchParams(url.split('?')[1]);

                    // 获取id参数的值（租户ID）
                    const id = params.get('id');
                    // 兼容旧版本，如果没有id参数，则尝试获取comName参数
                    const comName = params.get('comName');

                    if (!id && !comName) {
                        console.error('未提供公司ID或名称参数');
                        return;
                    }

                    try {
                        let response;

                        // 优先使用id参数调用getById接口
                        if (id) {
                            response = await fetch(jeeApi + '/jeecg-boot/sys/tenant/getById?id=' + encodeURIComponent(id), {
                                method: 'get',
                            });
                        } else {
                            // 兼容旧版本，如果没有id参数，则使用comName参数
                            response = await fetch(jeeApi + '/jeecg-boot/sys/tenant/getByComName?comName=' + encodeURIComponent(comName), {
                                method: 'get',
                            });
                        }

                        const data = await response.json();

                        if (data.success && data.result) {
                            this.companyDetail = data.result;
                            document.title = this.companyDetail.name + ' - 公司详情';
                        } else {
                            console.error('获取公司详情失败:', data.message || '未知错误');
                        }
                    } catch (error) {
                        console.error('获取公司详情出错:', error);
                    }
                },
            }
        })
    </script>
</body>

</html>
